using UnityEngine;
using Pathfinding;
using System.Collections;

namespace Pathfinding
{
    /// <summary>
    /// Non-ECS movement script similar to FollowerEntity but using traditional MonoBehaviour approach.
    /// 
    /// This script provides smooth pathfinding movement with features like:
    /// - Automatic pathfinding and following
    /// - Smooth rotation and movement
    /// - Configurable speed and rotation parameters
    /// - Support for stopping distance and end-of-path detection
    /// - Compatible with RVO local avoidance
    /// </summary>
    [RequireComponent(typeof(Seeker))]
    [AddComponentMenu("Pathfinding/AI/FollowerAI (Non-ECS)")]
    [HelpURL("https://arongranberg.com/astar/documentation/stable/followerAI.html")]
    public class FollowerAI : VersionedMonoBehaviour, IAstarAI
    {
        #region Movement Settings
        [Header("Movement")]
        /// <summary>Maximum movement speed in units per second</summary>
        public float speed = 5f;
        
        /// <summary>Maximum rotation speed in degrees per second</summary>
        public float rotationSpeed = 360f;
        
        /// <summary>Maximum rotation speed when rotating on the spot in degrees per second</summary>
        public float maxOnSpotRotationSpeed = 720f;
        
        /// <summary>Time in seconds for the agent to slow down to a stop</summary>
        public float slowdownTime = 0.5f;
        
        /// <summary>Time in seconds for the agent to slow down when turning on spot</summary>
        public float slowdownTimeWhenTurningOnSpot = 0f;
        
        /// <summary>Desired distance to walls and obstacles</summary>
        public float desiredWallDistance = 0.5f;
        
        /// <summary>Radius around the destination where the agent will start to slow down</summary>
        public float leadInRadiusWhenApproachingDestination = 1f;
        
        /// <summary>Allow the agent to rotate on the spot when backing away from obstacles</summary>
        public bool allowRotatingOnSpotBacking = true;
        
        /// <summary>Distance from the target point where the agent will be considered to have reached it</summary>
        public float stopDistance = 0.2f;
        
        /// <summary>Smoothing factor for rotation (0 = no smoothing, higher values = more smoothing)</summary>
        public float rotationSmoothing = 0f;
        
        /// <summary>Smoothing factor for position (0 = no smoothing, higher values = more smoothing)</summary>
        public float positionSmoothing = 0f;
        
        /// <summary>Layer mask for ground detection</summary>
        public LayerMask groundMask = -1;
        
        /// <summary>Whether the agent should move or not</summary>
        public bool canMove = true;
        
        /// <summary>Whether the agent should update its rotation</summary>
        public bool updateRotation = true;
        
        /// <summary>Whether the agent should update its position</summary>
        public bool updatePosition = true;
        
        /// <summary>Whether the agent is currently stopped</summary>
        public bool isStopped = false;
        #endregion
        
        #region Pathfinding Settings
        [Header("Pathfinding")]
        /// <summary>Automatic repath settings</summary>
        public AutoRepathPolicy autoRepath = new AutoRepathPolicy();
        
        /// <summary>Graph mask for pathfinding</summary>
        public GraphMask graphMask = -1;
        
        /// <summary>Tag penalties for pathfinding</summary>
        public int[] tagPenalties = new int[32];
        
        /// <summary>Traversable tags for pathfinding</summary>
        public int traversableTags = -1;
        #endregion
        
        #region RVO Settings
        [Header("Local Avoidance")]
        /// <summary>Enable local avoidance (RVO)</summary>
        public bool enableLocalAvoidance = true;

        /// <summary>RVO agent time horizon</summary>
        public float agentTimeHorizon = 1f;

        /// <summary>RVO obstacle time horizon</summary>
        public float obstacleTimeHorizon = 0.5f;

        /// <summary>Maximum number of RVO neighbors</summary>
        public int maxNeighbours = 10;

        /// <summary>RVO layer</summary>
        public RVOLayer rvoLayer = RVOLayer.DefaultAgent;

        /// <summary>RVO collision layers</summary>
        public RVOLayer collidesWith = (RVOLayer)(-1);

        /// <summary>RVO priority (0-1, higher values have higher priority)</summary>
        public float priority = 0.5f;
        #endregion

        #region Agent Shape
        [Header("Agent Shape")]
        /// <summary>Radius of the agent</summary>
        public float agentRadius = 0.5f;

        /// <summary>Height of the agent</summary>
        public float agentHeight = 2f;
        #endregion
        
        #region Private Fields
        private Seeker seeker;
        private RVOController rvoController;
        private Path currentPath;
        private int currentWaypoint = 0;
        private Vector3 targetDestination;
        private Vector3 currentVelocity;
        private Vector3 lastPosition;
        private float lastRepath = -1f;
        private bool destinationSet = false;
        private bool reachedEndOfPath = false;
        private Vector3 steeringTarget;
        #endregion
        
        #region IAstarAI Implementation
        /// <summary>Current destination of the agent</summary>
        public Vector3 destination
        {
            get { return targetDestination; }
            set { SetDestination(value); }
        }
        
        /// <summary>Current velocity of the agent</summary>
        public Vector3 velocity
        {
            get { return currentVelocity; }
        }
        
        /// <summary>Desired velocity of the agent (before local avoidance)</summary>
        public Vector3 desiredVelocity { get; private set; }
        
        /// <summary>Point that the agent is currently moving towards</summary>
        public Vector3 steeringTarget
        {
            get { return this.steeringTarget; }
        }
        
        /// <summary>True if the agent has reached the end of the current path</summary>
        public bool reachedEndOfPath
        {
            get { return this.reachedEndOfPath; }
        }
        
        /// <summary>True if the agent has reached its destination</summary>
        public bool reachedDestination
        {
            get { return reachedEndOfPath && Vector3.Distance(transform.position, targetDestination) <= stopDistance; }
        }
        
        /// <summary>True if a path is currently being calculated</summary>
        public bool pathPending
        {
            get { return seeker.IsDone() == false; }
        }
        
        /// <summary>True if the agent can move</summary>
        bool IAstarAI.canMove
        {
            get { return this.canMove; }
            set { this.canMove = value; }
        }
        
        /// <summary>True if the agent can search for paths</summary>
        public bool canSearch { get; set; } = true;
        
        /// <summary>Maximum speed of the agent</summary>
        public float maxSpeed
        {
            get { return speed; }
            set { speed = value; }
        }

        /// <summary>Radius of the agent</summary>
        public float radius
        {
            get { return agentRadius; }
            set { agentRadius = value; }
        }

        /// <summary>Height of the agent</summary>
        public float height
        {
            get { return agentHeight; }
            set { agentHeight = value; }
        }

        /// <summary>Position of the agent</summary>
        public Vector3 position
        {
            get { return transform.position; }
        }

        /// <summary>Rotation of the agent</summary>
        public Quaternion rotation
        {
            get { return transform.rotation; }
        }

        /// <summary>Desired velocity without local avoidance applied</summary>
        public Vector3 desiredVelocityWithoutLocalAvoidance
        {
            get { return desiredVelocity; }
        }

        /// <summary>Remaining distance to the destination</summary>
        public float remainingDistance
        {
            get { return GetRemainingDistance(); }
        }

        /// <summary>True if the agent has reached the end of the current path</summary>
        public bool endOfPath
        {
            get { return reachedEndOfPath; }
        }

        /// <summary>True if the agent has a path</summary>
        public bool hasPath
        {
            get { return currentPath != null && !currentPath.error; }
        }

        /// <summary>Whether the agent should update its position</summary>
        bool IAstarAI.updatePosition
        {
            get { return updatePosition; }
            set { updatePosition = value; }
        }

        /// <summary>Whether the agent should update its rotation</summary>
        bool IAstarAI.updateRotation
        {
            get { return updateRotation; }
            set { updateRotation = value; }
        }

        /// <summary>Whether the agent is currently stopped</summary>
        bool IAstarAI.isStopped
        {
            get { return isStopped; }
            set { isStopped = value; }
        }

        /// <summary>Event called when a path search is started</summary>
        public System.Action onSearchPath { get; set; }

        /// <summary>Movement plane for the agent</summary>
        public IMovementPlane movementPlane { get; set; }
        #endregion
        
        #region Unity Lifecycle
        protected override void Awake()
        {
            base.Awake();
            seeker = GetComponent<Seeker>();
            rvoController = GetComponent<RVOController>();

            // Initialize movement plane (default to XZ plane)
            movementPlane ??= GraphTransform.identityTransform;

            // Initialize RVO settings
            if (rvoController != null && enableLocalAvoidance)
            {
                rvoController.agentTimeHorizon = agentTimeHorizon;
                rvoController.obstacleTimeHorizon = obstacleTimeHorizon;
                rvoController.maxNeighbours = maxNeighbours;
                rvoController.layer = rvoLayer;
                rvoController.collidesWith = collidesWith;
                rvoController.priority = priority;
            }

            lastPosition = transform.position;
        }
        
        void Start()
        {
            // Start automatic repathing if enabled
            if (autoRepath.mode != AutoRepathPolicy.Mode.Never)
            {
                StartCoroutine(AutoRepathCoroutine());
            }
        }
        
        void Update()
        {
            if (!canMove || isStopped) return;
            
            // Calculate current velocity
            currentVelocity = (transform.position - lastPosition) / Time.deltaTime;
            lastPosition = transform.position;
            
            // Update movement
            UpdateMovement();
        }
        #endregion
        
        #region Movement Logic
        private void UpdateMovement()
        {
            if (currentPath == null || currentPath.error)
            {
                desiredVelocity = Vector3.zero;
                return;
            }
            
            // Check if we've reached the end of the path
            if (currentWaypoint >= currentPath.vectorPath.Count)
            {
                reachedEndOfPath = true;
                desiredVelocity = Vector3.zero;
                return;
            }
            
            reachedEndOfPath = false;
            
            // Get the current target waypoint
            Vector3 currentTarget = currentPath.vectorPath[currentWaypoint];
            steeringTarget = currentTarget;
            
            // Check if we should move to the next waypoint
            float distanceToWaypoint = Vector3.Distance(transform.position, currentTarget);
            if (distanceToWaypoint < 1f && currentWaypoint < currentPath.vectorPath.Count - 1)
            {
                currentWaypoint++;
                currentTarget = currentPath.vectorPath[currentWaypoint];
                steeringTarget = currentTarget;
            }
            
            // Calculate desired velocity
            Vector3 direction = (currentTarget - transform.position).normalized;
            float targetSpeed = speed;
            
            // Slow down when approaching destination
            float distanceToDestination = Vector3.Distance(transform.position, targetDestination);
            if (distanceToDestination < leadInRadiusWhenApproachingDestination)
            {
                targetSpeed *= Mathf.Clamp01(distanceToDestination / leadInRadiusWhenApproachingDestination);
            }
            
            desiredVelocity = direction * targetSpeed;
            
            // Apply movement
            Vector3 actualVelocity = desiredVelocity;
            
            // Use RVO if available
            if (rvoController != null && enableLocalAvoidance)
            {
                rvoController.SetTarget(transform.position + desiredVelocity, desiredVelocity.magnitude, speed);
                actualVelocity = rvoController.CalculateMovementDelta(Time.deltaTime) / Time.deltaTime;
            }
            
            // Apply position update
            if (updatePosition && canMove)
            {
                Vector3 newPosition = transform.position + actualVelocity * Time.deltaTime;
                
                if (positionSmoothing > 0)
                {
                    transform.position = Vector3.Lerp(transform.position, newPosition, Time.deltaTime / positionSmoothing);
                }
                else
                {
                    transform.position = newPosition;
                }
            }
            
            // Apply rotation update
            if (updateRotation && actualVelocity.magnitude > 0.1f)
            {
                Quaternion targetRotation = Quaternion.LookRotation(actualVelocity.normalized);
                
                if (rotationSmoothing > 0)
                {
                    transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, Time.deltaTime / rotationSmoothing);
                }
                else
                {
                    float maxRotation = rotationSpeed * Time.deltaTime;
                    transform.rotation = Quaternion.RotateTowards(transform.rotation, targetRotation, maxRotation);
                }
            }
        }
        #endregion

        #region IAstarAI Methods
        /// <summary>Get the remaining path</summary>
        public void GetRemainingPath(List<Vector3> buffer, out bool stale)
        {
            buffer.Clear();
            stale = false;

            if (currentPath == null || currentPath.error)
            {
                stale = true;
                return;
            }

            for (int i = currentWaypoint; i < currentPath.vectorPath.Count; i++)
            {
                buffer.Add(currentPath.vectorPath[i]);
            }
        }

        /// <summary>Get the remaining path with link info</summary>
        public void GetRemainingPath(List<Vector3> buffer, List<PathPartWithLinkInfo> partsBuffer, out bool stale)
        {
            GetRemainingPath(buffer, out stale);
            partsBuffer.Clear();

            if (!stale && buffer.Count > 0)
            {
                partsBuffer.Add(new PathPartWithLinkInfo
                {
                    startIndex = 0,
                    endIndex = buffer.Count,
                    type = PathPartWithLinkInfo.PathPartType.Normal
                });
            }
        }

        /// <summary>Set the path for the agent</summary>
        public void SetPath(Path path, bool updateDestination = true)
        {
            if (path.error)
            {
                Debug.LogWarning("Cannot set path with error: " + path.errorLog);
                return;
            }

            currentPath = path;
            currentWaypoint = 0;
            reachedEndOfPath = false;

            if (updateDestination && path.vectorPath.Count > 0)
            {
                targetDestination = path.vectorPath[path.vectorPath.Count - 1];
                destinationSet = true;
            }
        }

        /// <summary>Move the agent by a relative amount</summary>
        public void Move(Vector3 deltaPosition)
        {
            if (updatePosition)
            {
                transform.position += deltaPosition;
                lastPosition = transform.position;
            }
        }

        /// <summary>Finalize movement for this frame</summary>
        public void FinalizeMovement(Vector3 nextPosition, Quaternion nextRotation)
        {
            if (updatePosition)
            {
                transform.position = nextPosition;
                lastPosition = nextPosition;
            }

            if (updateRotation)
            {
                transform.rotation = nextRotation;
            }
        }
        #endregion

        #region Pathfinding Methods
        /// <summary>Set the destination for the agent</summary>
        public void SetDestination(Vector3 destination)
        {
            targetDestination = destination;
            destinationSet = true;
            SearchPath();
        }

        /// <summary>Set the destination with a facing direction</summary>
        public void SetDestination(Vector3 destination, Vector3 facingDirection)
        {
            SetDestination(destination);
            // Note: Facing direction handling would require additional implementation
        }

        /// <summary>Search for a path to the current destination</summary>
        public void SearchPath()
        {
            if (!canSearch || !destinationSet || seeker == null) return;

            onSearchPath?.Invoke();
            seeker.StartPath(transform.position, targetDestination, OnPathComplete);
        }

        /// <summary>Called when a path calculation is complete</summary>
        private void OnPathComplete(Path path)
        {
            if (path.error)
            {
                Debug.LogWarning("Path calculation failed: " + path.errorLog);
                return;
            }

            currentPath = path;
            currentWaypoint = 0;
            reachedEndOfPath = false;
        }

        /// <summary>Get the remaining distance to the destination</summary>
        public float GetRemainingDistance()
        {
            if (currentPath == null || currentPath.error || reachedEndOfPath)
                return float.PositiveInfinity;

            float distance = 0f;
            Vector3 currentPos = transform.position;

            for (int i = currentWaypoint; i < currentPath.vectorPath.Count; i++)
            {
                distance += Vector3.Distance(currentPos, currentPath.vectorPath[i]);
                currentPos = currentPath.vectorPath[i];
            }

            return distance;
        }

        /// <summary>Teleport the agent to a position</summary>
        public void Teleport(Vector3 position, bool clearPath = true)
        {
            transform.position = position;
            lastPosition = position;

            if (clearPath)
            {
                currentPath = null;
                currentWaypoint = 0;
                reachedEndOfPath = false;
            }

            rvoController?.Move(Vector3.zero);
        }

        /// <summary>Stop the agent</summary>
        public void Stop()
        {
            isStopped = true;
            desiredVelocity = Vector3.zero;
        }

        /// <summary>Resume movement</summary>
        public void Resume()
        {
            isStopped = false;
        }
        #endregion

        #region Auto Repath
        private IEnumerator AutoRepathCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(autoRepath.period);

                if (autoRepath.mode == AutoRepathPolicy.Mode.Never) break;

                if (destinationSet && !pathPending && canSearch)
                {
                    // Check if we need to repath based on distance moved
                    if (autoRepath.mode == AutoRepathPolicy.Mode.Dynamic)
                    {
                        float distanceMoved = Vector3.Distance(transform.position, lastPosition);
                        if (distanceMoved > autoRepath.sensitivity)
                        {
                            SearchPath();
                        }
                    }
                    else if (autoRepath.mode == AutoRepathPolicy.Mode.EveryNSeconds)
                    {
                        SearchPath();
                    }
                }
            }
        }
        #endregion

        #region Movement Update (for external control)
        /// <summary>Calculate movement for this frame (used by external systems like Mecanim)</summary>
        public void MovementUpdate(float deltaTime, out Vector3 nextPosition, out Quaternion nextRotation)
        {
            nextPosition = transform.position;
            nextRotation = transform.rotation;

            if (!canMove || isStopped || currentPath == null || currentPath.error)
                return;

            // This is a simplified version - full implementation would be more complex
            if (currentWaypoint < currentPath.vectorPath.Count)
            {
                Vector3 direction = (currentPath.vectorPath[currentWaypoint] - transform.position).normalized;
                nextPosition = transform.position + direction * speed * deltaTime;

                if (direction.magnitude > 0.1f)
                {
                    nextRotation = Quaternion.LookRotation(direction);
                }
            }
        }
        #endregion
    }

    /// <summary>Auto repath policy settings</summary>
    [System.Serializable]
    public class AutoRepathPolicy
    {
        public enum Mode
        {
            Never,
            EveryNSeconds,
            Dynamic
        }

        public Mode mode = Mode.EveryNSeconds;
        public float period = 0.5f;
        public float sensitivity = 10f;
        public float maximumPeriod = 2f;
    }
}
