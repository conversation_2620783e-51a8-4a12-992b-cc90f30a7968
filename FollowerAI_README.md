# FollowerAI - Non-ECS Movement Script

FollowerAI is a traditional MonoBehaviour-based movement script that provides similar functionality to FollowerEntity but without using Unity's Entity Component System (ECS). It's designed to be a drop-in replacement for scenarios where you need pathfinding AI but want to avoid ECS complexity.

## Features

- **Smooth Pathfinding Movement**: Automatic path calculation and following using A* Pathfinding Project
- **Configurable Speed Settings**: Adjustable movement speed, rotation speed, and acceleration
- **Local Avoidance Support**: Compatible with RVO (Reciprocal Velocity Obstacles) for crowd simulation
- **Automatic Repathing**: Configurable automatic path recalculation
- **IAstarAI Interface**: Implements the standard A* Pathfinding Project interface for compatibility
- **Smooth Movement**: Optional position and rotation smoothing
- **Stop Distance**: Configurable distance for destination reaching
- **External Control**: Support for external movement systems like Mecanim

## Setup

### Basic Setup

1. **Add Required Components**:
   - Add `FollowerAI` component to your GameObject
   - Ensure `Seeker` component is present (automatically added by RequireComponent)
   - Optionally add `RVOController` for local avoidance

2. **Configure Movement Settings**:
   ```csharp
   // Get the component
   FollowerAI follower = GetComponent<FollowerAI>();
   
   // Set basic movement parameters
   follower.speed = 5f;
   follower.rotationSpeed = 360f;
   follower.stopDistance = 0.2f;
   ```

3. **Set Destination**:
   ```csharp
   // Set a destination
   follower.SetDestination(targetPosition);
   
   // Or use the property
   follower.destination = targetPosition;
   ```

### Advanced Setup

#### Local Avoidance (RVO)
```csharp
// Enable RVO settings
follower.enableLocalAvoidance = true;
follower.agentTimeHorizon = 1f;
follower.obstacleTimeHorizon = 0.5f;
follower.maxNeighbours = 10;
follower.priority = 0.5f;
```

#### Automatic Repathing
```csharp
// Configure automatic repathing
follower.autoRepath.mode = AutoRepathPolicy.Mode.EveryNSeconds;
follower.autoRepath.period = 0.5f;
follower.autoRepath.sensitivity = 10f;
```

#### Smoothing
```csharp
// Add movement smoothing
follower.positionSmoothing = 0.1f;
follower.rotationSmoothing = 0.1f;
```

## Usage Examples

### Basic Movement
```csharp
public class BasicMovement : MonoBehaviour
{
    public Transform target;
    private FollowerAI follower;
    
    void Start()
    {
        follower = GetComponent<FollowerAI>();
        follower.SetDestination(target.position);
    }
    
    void Update()
    {
        // Update destination if target moves
        if (Vector3.Distance(follower.destination, target.position) > 1f)
        {
            follower.SetDestination(target.position);
        }
    }
}
```

### Waypoint Patrol
```csharp
public class WaypointPatrol : MonoBehaviour
{
    public Transform[] waypoints;
    private FollowerAI follower;
    private int currentWaypoint = 0;
    
    void Start()
    {
        follower = GetComponent<FollowerAI>();
        if (waypoints.Length > 0)
            follower.SetDestination(waypoints[0].position);
    }
    
    void Update()
    {
        if (follower.reachedDestination)
        {
            currentWaypoint = (currentWaypoint + 1) % waypoints.Length;
            follower.SetDestination(waypoints[currentWaypoint].position);
        }
    }
}
```

### Stop and Resume
```csharp
// Stop the agent
follower.Stop();

// Resume movement
follower.Resume();

// Check if stopped
if (follower.isStopped)
{
    // Agent is currently stopped
}
```

### Status Checking
```csharp
// Check various status properties
if (follower.reachedDestination)
{
    Debug.Log("Reached destination!");
}

if (follower.pathPending)
{
    Debug.Log("Still calculating path...");
}

Debug.Log($"Current speed: {follower.velocity.magnitude}");
Debug.Log($"Distance remaining: {follower.GetRemainingDistance()}");
```

## Key Differences from FollowerEntity

| Feature | FollowerEntity (ECS) | FollowerAI (MonoBehaviour) |
|---------|---------------------|---------------------------|
| Architecture | Entity Component System | Traditional MonoBehaviour |
| Performance | Higher (burst compiled) | Standard (managed code) |
| Setup Complexity | More complex | Simpler |
| Debugging | ECS debugger required | Standard Unity debugging |
| Integration | ECS systems | Standard Unity components |
| Memory Usage | Lower | Higher |

## Configuration Reference

### Movement Settings
- `speed`: Maximum movement speed (units/second)
- `rotationSpeed`: Maximum rotation speed (degrees/second)
- `maxOnSpotRotationSpeed`: Max rotation when turning in place
- `slowdownTime`: Time to decelerate to stop
- `desiredWallDistance`: Preferred distance from obstacles
- `stopDistance`: Distance to consider destination reached

### Pathfinding Settings
- `autoRepath`: Automatic path recalculation settings
- `graphMask`: Which graphs to use for pathfinding
- `tagPenalties`: Cost penalties for different node tags
- `traversableTags`: Which tagged nodes can be traversed

### RVO Settings
- `enableLocalAvoidance`: Enable/disable local avoidance
- `agentTimeHorizon`: Time horizon for agent avoidance
- `obstacleTimeHorizon`: Time horizon for obstacle avoidance
- `maxNeighbours`: Maximum neighbors to consider
- `priority`: Agent priority (0-1, higher = more priority)

## Performance Considerations

- **Single Agents**: FollowerAI is perfect for small numbers of agents
- **Large Crowds**: Consider FollowerEntity for 100+ agents
- **Memory**: MonoBehaviour approach uses more memory per agent
- **CPU**: No burst compilation, but easier to optimize manually

## Troubleshooting

### Agent Not Moving
1. Check if `canMove` is true
2. Verify `Seeker` component is present
3. Ensure destination is set and reachable
4. Check if agent is stopped (`isStopped`)

### Jerky Movement
1. Increase `positionSmoothing` and `rotationSmoothing`
2. Adjust `slowdownTime` for smoother deceleration
3. Check frame rate and Time.deltaTime

### Path Not Found
1. Verify graphs are properly configured
2. Check `graphMask` settings
3. Ensure start and end positions are on the graph
4. Review `traversableTags` configuration

## Integration with Other Systems

### Mecanim Animation
```csharp
// Use MovementUpdate for external control
Vector3 nextPos;
Quaternion nextRot;
follower.MovementUpdate(Time.deltaTime, out nextPos, out nextRot);

// Apply to animator or other system
animator.SetFloat("Speed", follower.velocity.magnitude);
```

### Custom Movement Override
```csharp
// Disable automatic movement
follower.updatePosition = false;
follower.updateRotation = false;

// Use desired velocity for custom movement
Vector3 customMovement = follower.desiredVelocity * Time.deltaTime;
transform.position += customMovement;
```

This FollowerAI script provides a robust, easy-to-use alternative to ECS-based movement while maintaining compatibility with the A* Pathfinding Project ecosystem.
