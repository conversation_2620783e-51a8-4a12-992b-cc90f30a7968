using UnityEngine;
using Pathfinding;

/// <summary>
/// Example script showing how to use the FollowerAI component.
/// This demonstrates basic usage patterns and common scenarios.
/// </summary>
public class FollowerAIExample : MonoBehaviour
{
    [Header("Target Settings")]
    public Transform target;
    public bool followTarget = true;
    public float followDistance = 2f;
    
    [Header("Waypoint Settings")]
    public Transform[] waypoints;
    public bool patrolWaypoints = false;
    public float waitTimeAtWaypoint = 2f;
    
    [Header("Controls")]
    public KeyCode stopKey = KeyCode.Space;
    public KeyCode resumeKey = KeyCode.R;
    public KeyCode teleportKey = KeyCode.T;
    public Vector3 teleportPosition = Vector3.zero;
    
    private FollowerAI followerAI;
    private int currentWaypointIndex = 0;
    private float waypointTimer = 0f;
    private bool isWaiting = false;
    
    void Start()
    {
        // Get the FollowerAI component
        followerAI = GetComponent<FollowerAI>();
        
        if (followerAI == null)
        {
            Debug.LogError("FollowerAI component not found! Please add a FollowerAI component to this GameObject.");
            enabled = false;
            return;
        }
        
        // Start following target or patrolling waypoints
        if (followTarget && target != null)
        {
            followerAI.SetDestination(target.position);
        }
        else if (patrolWaypoints && waypoints.Length > 0)
        {
            followerAI.SetDestination(waypoints[0].position);
        }
    }
    
    void Update()
    {
        HandleInput();
        
        if (followTarget && target != null)
        {
            HandleTargetFollowing();
        }
        else if (patrolWaypoints && waypoints.Length > 0)
        {
            HandleWaypointPatrol();
        }
    }
    
    private void HandleInput()
    {
        // Stop/Resume controls
        if (Input.GetKeyDown(stopKey))
        {
            followerAI.Stop();
            Debug.Log("FollowerAI stopped");
        }
        
        if (Input.GetKeyDown(resumeKey))
        {
            followerAI.Resume();
            Debug.Log("FollowerAI resumed");
        }
        
        // Teleport control
        if (Input.GetKeyDown(teleportKey))
        {
            followerAI.Teleport(teleportPosition);
            Debug.Log($"FollowerAI teleported to {teleportPosition}");
        }
        
        // Mouse click to set destination
        if (Input.GetMouseButtonDown(0))
        {
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            RaycastHit hit;
            
            if (Physics.Raycast(ray, out hit))
            {
                followerAI.SetDestination(hit.point);
                Debug.Log($"New destination set: {hit.point}");
                
                // Disable automatic behaviors when manually setting destination
                followTarget = false;
                patrolWaypoints = false;
            }
        }
    }
    
    private void HandleTargetFollowing()
    {
        if (target == null) return;
        
        float distanceToTarget = Vector3.Distance(transform.position, target.position);
        
        // Only update destination if target has moved significantly or we're too far
        if (distanceToTarget > followDistance + 1f || 
            Vector3.Distance(followerAI.destination, target.position) > 1f)
        {
            // Calculate position near target but not exactly on it
            Vector3 directionToTarget = (target.position - transform.position).normalized;
            Vector3 followPosition = target.position - directionToTarget * followDistance;
            
            followerAI.SetDestination(followPosition);
        }
    }
    
    private void HandleWaypointPatrol()
    {
        if (waypoints.Length == 0) return;
        
        // Check if we've reached the current waypoint
        if (followerAI.reachedDestination)
        {
            if (!isWaiting)
            {
                // Start waiting at waypoint
                isWaiting = true;
                waypointTimer = 0f;
                Debug.Log($"Reached waypoint {currentWaypointIndex}, waiting for {waitTimeAtWaypoint} seconds");
            }
            else
            {
                // Continue waiting
                waypointTimer += Time.deltaTime;
                
                if (waypointTimer >= waitTimeAtWaypoint)
                {
                    // Move to next waypoint
                    currentWaypointIndex = (currentWaypointIndex + 1) % waypoints.Length;
                    followerAI.SetDestination(waypoints[currentWaypointIndex].position);
                    isWaiting = false;
                    Debug.Log($"Moving to waypoint {currentWaypointIndex}");
                }
            }
        }
    }
    
    // Gizmos for visualization in Scene view
    void OnDrawGizmosSelected()
    {
        if (followerAI != null)
        {
            // Draw destination
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(followerAI.destination, 0.5f);
            
            // Draw line to destination
            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(transform.position, followerAI.destination);
            
            // Draw steering target
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(followerAI.steeringTarget, 0.3f);
        }
        
        // Draw waypoints
        if (waypoints != null && waypoints.Length > 0)
        {
            Gizmos.color = Color.blue;
            for (int i = 0; i < waypoints.Length; i++)
            {
                if (waypoints[i] != null)
                {
                    Gizmos.DrawWireSphere(waypoints[i].position, 0.5f);
                    
                    // Draw connections between waypoints
                    if (i < waypoints.Length - 1 && waypoints[i + 1] != null)
                    {
                        Gizmos.DrawLine(waypoints[i].position, waypoints[i + 1].position);
                    }
                    else if (i == waypoints.Length - 1 && waypoints[0] != null)
                    {
                        // Connect last waypoint back to first for patrol loop
                        Gizmos.DrawLine(waypoints[i].position, waypoints[0].position);
                    }
                }
            }
        }
        
        // Draw follow distance
        if (followTarget && target != null)
        {
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(target.position, followDistance);
        }
    }
    
    // Display status information in the inspector
    void OnGUI()
    {
        if (followerAI == null) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("FollowerAI Status:", EditorGUIUtility.isProSkin ? GUI.skin.box : GUI.skin.label);
        GUILayout.Label($"Velocity: {followerAI.velocity.magnitude:F2} m/s");
        GUILayout.Label($"Desired Velocity: {followerAI.desiredVelocity.magnitude:F2} m/s");
        GUILayout.Label($"Reached End: {followerAI.reachedEndOfPath}");
        GUILayout.Label($"Reached Destination: {followerAI.reachedDestination}");
        GUILayout.Label($"Path Pending: {followerAI.pathPending}");
        GUILayout.Label($"Can Move: {followerAI.canMove}");
        GUILayout.Label($"Distance to Destination: {Vector3.Distance(transform.position, followerAI.destination):F2}");
        
        GUILayout.Space(10);
        GUILayout.Label("Controls:");
        GUILayout.Label($"Stop: {stopKey}");
        GUILayout.Label($"Resume: {resumeKey}");
        GUILayout.Label($"Teleport: {teleportKey}");
        GUILayout.Label("Left Click: Set Destination");
        
        GUILayout.EndArea();
    }
}
