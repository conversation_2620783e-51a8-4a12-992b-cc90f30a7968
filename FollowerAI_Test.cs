using UnityEngine;
using Pathfinding;

/// <summary>
/// Simple test script to verify FollowerAI functionality
/// </summary>
public class FollowerAI_Test : MonoBehaviour
{
    [Header("Test Settings")]
    public Transform testTarget;
    public bool runTests = true;
    
    private FollowerAI followerAI;
    
    void Start()
    {
        followerAI = GetComponent<FollowerAI>();
        
        if (followerAI == null)
        {
            Debug.LogError("FollowerAI component not found!");
            return;
        }
        
        if (runTests)
        {
            StartCoroutine(RunTests());
        }
    }
    
    System.Collections.IEnumerator RunTests()
    {
        Debug.Log("Starting FollowerAI Tests...");
        
        // Test 1: Basic Interface Implementation
        TestInterfaceImplementation();
        yield return new WaitForSeconds(1f);
        
        // Test 2: Basic Movement
        if (testTarget != null)
        {
            TestBasicMovement();
            yield return new WaitForSeconds(3f);
        }
        
        // Test 3: Stop and Resume
        TestStopResume();
        yield return new WaitForSeconds(2f);
        
        // Test 4: Properties
        TestProperties();
        
        Debug.Log("FollowerAI Tests Completed!");
    }
    
    void TestInterfaceImplementation()
    {
        Debug.Log("Test 1: Interface Implementation");
        
        // Test IAstarAI interface
        IAstarAI ai = followerAI;
        
        Debug.Log($"✓ destination: {ai.destination}");
        Debug.Log($"✓ velocity: {ai.velocity}");
        Debug.Log($"✓ desiredVelocity: {ai.desiredVelocity}");
        Debug.Log($"✓ steeringTarget: {ai.steeringTarget}");
        Debug.Log($"✓ reachedEndOfPath: {ai.reachedEndOfPath}");
        Debug.Log($"✓ reachedDestination: {ai.reachedDestination}");
        Debug.Log($"✓ pathPending: {ai.pathPending}");
        Debug.Log($"✓ canMove: {ai.canMove}");
        Debug.Log($"✓ canSearch: {ai.canSearch}");
        Debug.Log($"✓ maxSpeed: {ai.maxSpeed}");
        Debug.Log($"✓ radius: {ai.radius}");
        Debug.Log($"✓ height: {ai.height}");
        Debug.Log($"✓ position: {ai.position}");
        Debug.Log($"✓ rotation: {ai.rotation}");
        Debug.Log($"✓ remainingDistance: {ai.remainingDistance}");
        Debug.Log($"✓ endOfPath: {ai.endOfPath}");
        Debug.Log($"✓ hasPath: {ai.hasPath}");
        Debug.Log($"✓ updatePosition: {ai.updatePosition}");
        Debug.Log($"✓ updateRotation: {ai.updateRotation}");
        Debug.Log($"✓ isStopped: {ai.isStopped}");
        Debug.Log($"✓ movementPlane: {ai.movementPlane}");
        
        Debug.Log("✓ All IAstarAI properties accessible!");
    }
    
    void TestBasicMovement()
    {
        Debug.Log("Test 2: Basic Movement");
        
        Vector3 startPos = transform.position;
        followerAI.SetDestination(testTarget.position);
        
        Debug.Log($"✓ Set destination to: {testTarget.position}");
        Debug.Log($"✓ Starting from: {startPos}");
        Debug.Log($"✓ Path pending: {followerAI.pathPending}");
    }
    
    void TestStopResume()
    {
        Debug.Log("Test 3: Stop and Resume");
        
        // Test stop
        followerAI.Stop();
        Debug.Log($"✓ Stopped agent. isStopped: {followerAI.isStopped}");
        
        // Test resume
        followerAI.Resume();
        Debug.Log($"✓ Resumed agent. isStopped: {followerAI.isStopped}");
    }
    
    void TestProperties()
    {
        Debug.Log("Test 4: Properties");
        
        // Test property setters
        float originalSpeed = followerAI.speed;
        followerAI.maxSpeed = 10f;
        Debug.Log($"✓ Set maxSpeed to 10, speed is now: {followerAI.speed}");
        
        float originalRadius = followerAI.radius;
        followerAI.radius = 1f;
        Debug.Log($"✓ Set radius to 1, radius is now: {followerAI.radius}");
        
        float originalHeight = followerAI.height;
        followerAI.height = 3f;
        Debug.Log($"✓ Set height to 3, height is now: {followerAI.height}");
        
        // Test movement control
        bool originalCanMove = followerAI.canMove;
        followerAI.canMove = false;
        Debug.Log($"✓ Set canMove to false: {followerAI.canMove}");
        followerAI.canMove = originalCanMove;
        
        // Test update flags
        bool originalUpdatePos = followerAI.updatePosition;
        followerAI.updatePosition = false;
        Debug.Log($"✓ Set updatePosition to false: {followerAI.updatePosition}");
        followerAI.updatePosition = originalUpdatePos;
        
        bool originalUpdateRot = followerAI.updateRotation;
        followerAI.updateRotation = false;
        Debug.Log($"✓ Set updateRotation to false: {followerAI.updateRotation}");
        followerAI.updateRotation = originalUpdateRot;
    }
    
    void Update()
    {
        if (followerAI == null) return;
        
        // Display real-time info
        if (Input.GetKeyDown(KeyCode.I))
        {
            Debug.Log("=== FollowerAI Status ===");
            Debug.Log($"Position: {followerAI.position}");
            Debug.Log($"Destination: {followerAI.destination}");
            Debug.Log($"Velocity: {followerAI.velocity.magnitude:F2} m/s");
            Debug.Log($"Desired Velocity: {followerAI.desiredVelocity.magnitude:F2} m/s");
            Debug.Log($"Reached Destination: {followerAI.reachedDestination}");
            Debug.Log($"Has Path: {followerAI.hasPath}");
            Debug.Log($"Path Pending: {followerAI.pathPending}");
            Debug.Log($"Remaining Distance: {followerAI.remainingDistance:F2}");
        }
        
        // Test teleport
        if (Input.GetKeyDown(KeyCode.T))
        {
            Vector3 randomPos = transform.position + Random.insideUnitSphere * 5f;
            randomPos.y = transform.position.y;
            followerAI.Teleport(randomPos);
            Debug.Log($"Teleported to: {randomPos}");
        }
        
        // Test move
        if (Input.GetKeyDown(KeyCode.M))
        {
            Vector3 moveOffset = Random.insideUnitSphere * 2f;
            moveOffset.y = 0;
            followerAI.Move(moveOffset);
            Debug.Log($"Moved by: {moveOffset}");
        }
    }
    
    void OnGUI()
    {
        if (followerAI == null) return;
        
        GUILayout.BeginArea(new Rect(10, Screen.height - 150, 400, 140));
        GUILayout.Label("FollowerAI Test Controls:", GUI.skin.box);
        GUILayout.Label("I - Show Status Info");
        GUILayout.Label("T - Random Teleport");
        GUILayout.Label("M - Random Move");
        GUILayout.Space(5);
        GUILayout.Label($"Status: {(followerAI.hasPath ? "Has Path" : "No Path")}");
        GUILayout.Label($"Speed: {followerAI.velocity.magnitude:F1} m/s");
        GUILayout.Label($"Distance: {followerAI.remainingDistance:F1}m");
        GUILayout.EndArea();
    }
}
