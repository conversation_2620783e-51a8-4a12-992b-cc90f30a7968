fileFormatVersion: 2
guid: 2d98bcf1edc7b9b43b7114afd7b37a0a
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 0
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 256
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 16384
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Isometric_Base_0
      rect:
        serializedVersion: 2
        x: 0
        y: 2304
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 008332dc0a824314a9a158f741bcfbeb
      internalID: -1624350362
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_1
      rect:
        serializedVersion: 2
        x: 512
        y: 2304
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2e24e6deaef3ad14cbc0163996a012ed
      internalID: 380641685
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_2
      rect:
        serializedVersion: 2
        x: 1024
        y: 2304
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ec52cc626e66ed44790f1a04038bede1
      internalID: 1438553515
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_3
      rect:
        serializedVersion: 2
        x: 1536
        y: 2304
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a796382d92f640c4fb1b69d523d30558
      internalID: -1127770414
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_4
      rect:
        serializedVersion: 2
        x: 0
        y: 1536
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3d1643c4dad9f2a478b282d5eee0d376
      internalID: 1690043711
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_5
      rect:
        serializedVersion: 2
        x: 512
        y: 1536
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 48f3f2325b0c134428789e2003e3168a
      internalID: -1957195661
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_6
      rect:
        serializedVersion: 2
        x: 1024
        y: 1536
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1057d68a6fc2baa4ab0fcd2a56ece4e2
      internalID: -932698716
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_7
      rect:
        serializedVersion: 2
        x: 1536
        y: 1536
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e45eb22548393194198844c94b5233c0
      internalID: -1114187093
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_8
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 97a6c24782632554391713d54fe853a1
      internalID: 1322942214
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_9
      rect:
        serializedVersion: 2
        x: 512
        y: 768
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 06bea0db3f85816469844ec6b582371e
      internalID: -697145162
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_10
      rect:
        serializedVersion: 2
        x: 1024
        y: 768
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ddf3b6a7bae346d40b8327eacffbeff0
      internalID: 1778511470
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_11
      rect:
        serializedVersion: 2
        x: 1536
        y: 768
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 751123f30377d8c45ab956563b582bba
      internalID: 2018422849
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_12
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fcee92622e4e0354b9d10e5792416976
      internalID: -706160425
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Base_13
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3d9ef6fc1927fa94e9bc0a967083f56b
      internalID: -768763849
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: ef2cb1e106a02f84698a2f5796752697
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Isometric_Base_0: -1624350362
      Isometric_Base_1: 380641685
      Isometric_Base_10: 1778511470
      Isometric_Base_11: 2018422849
      Isometric_Base_12: -706160425
      Isometric_Base_13: -768763849
      Isometric_Base_2: 1438553515
      Isometric_Base_3: -1127770414
      Isometric_Base_4: 1690043711
      Isometric_Base_5: -1957195661
      Isometric_Base_6: -932698716
      Isometric_Base_7: -1114187093
      Isometric_Base_8: 1322942214
      Isometric_Base_9: -697145162
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
