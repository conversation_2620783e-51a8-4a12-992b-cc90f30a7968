fileFormatVersion: 2
guid: e3a898d0e640be44998b5b22975174f1
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 0
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 256
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 8192
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 8192
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: 2D_Door_Animation_SpriteSheet_0
      rect:
        serializedVersion: 2
        x: 0
        y: 512
        width: 1536
        height: 512
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1281198bd1684ee48a4310cccfc9187c
      internalID: 38933764
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Door_Animation_SpriteSheet_1
      rect:
        serializedVersion: 2
        x: 1536
        y: 512
        width: 1536
        height: 512
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d2742d65cbfcca741bc931bc24ac52b5
      internalID: 1517098882
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Door_Animation_SpriteSheet_2
      rect:
        serializedVersion: 2
        x: 3072
        y: 512
        width: 1536
        height: 512
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6760d04915563c4458e4a36e2ff97d32
      internalID: 896561500
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Door_Animation_SpriteSheet_3
      rect:
        serializedVersion: 2
        x: 4608
        y: 512
        width: 1536
        height: 512
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ded31ec09d752174fa3fcad7f6f28ee4
      internalID: -1446503588
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Door_Animation_SpriteSheet_4
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 1536
        height: 512
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5728180501a1f5948ae9e5a1835e76e7
      internalID: -1888832583
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Door_Animation_SpriteSheet_5
      rect:
        serializedVersion: 2
        x: 1536
        y: 0
        width: 1536
        height: 512
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fcb5808394758014183a236501c322e0
      internalID: -1459498505
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Door_Animation_SpriteSheet_6
      rect:
        serializedVersion: 2
        x: 3072
        y: 0
        width: 1536
        height: 512
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2863bdb4a0eb79d4cb5ff49adc5c94fe
      internalID: -1887162575
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Door_Animation_SpriteSheet_7
      rect:
        serializedVersion: 2
        x: 4608
        y: 0
        width: 1536
        height: 512
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -754, y: -241}
        - {x: -747, y: 247}
        - {x: -264, y: 110}
        - {x: -264, y: -246}
      - - {x: 260, y: -243}
        - {x: 256, y: 113}
        - {x: 758, y: 236}
        - {x: 766, y: -247}
      tessellationDetail: 0
      bones: []
      spriteID: f4636036daac08e41a26393f06ffebf1
      internalID: -1609914005
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 812cab7a8ad508743a5abd53036c4698
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      2D_Door_Animation_SpriteSheet_0: 38933764
      2D_Door_Animation_SpriteSheet_1: 1517098882
      2D_Door_Animation_SpriteSheet_2: 896561500
      2D_Door_Animation_SpriteSheet_3: -1446503588
      2D_Door_Animation_SpriteSheet_4: -1888832583
      2D_Door_Animation_SpriteSheet_5: -1459498505
      2D_Door_Animation_SpriteSheet_6: -1887162575
      2D_Door_Animation_SpriteSheet_7: -1609914005
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
