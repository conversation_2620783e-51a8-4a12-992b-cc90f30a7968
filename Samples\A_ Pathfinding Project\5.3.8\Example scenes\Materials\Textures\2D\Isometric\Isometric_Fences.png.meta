fileFormatVersion: 2
guid: ef61442527d917947bbbce15981b0fde
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 0
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 256
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 16384
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Isometric_Fences_0
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a6cb413b451b6614c927aafff9846d95
      internalID: -554281435
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Fences_1
      rect:
        serializedVersion: 2
        x: 512
        y: 768
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 853212be9fb87e344ab0cbfbfe3603d5
      internalID: -509594886
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Fences_2
      rect:
        serializedVersion: 2
        x: 1024
        y: 768
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d88f6e5d790b8264c882bc0c62fcb7fe
      internalID: -856146300
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Fences_3
      rect:
        serializedVersion: 2
        x: 1536
        y: 768
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 542f95a8afc9bb645af9ccbcfc9b27ac
      internalID: -1947363811
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Fences_4
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 81988deb654e1814a98fc2fd070ece41
      internalID: -48743700
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Fences_5
      rect:
        serializedVersion: 2
        x: 512
        y: 384
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 66448fa9c1fb9d047a4ec3d717b95851
      internalID: 1029280952
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Fences_6
      rect:
        serializedVersion: 2
        x: 1024
        y: 384
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eb6d80104135f0f4e9859dc69eae2161
      internalID: 1035012717
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Fences_7
      rect:
        serializedVersion: 2
        x: 1536
        y: 384
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: de7295778bdcddf41b82b6e6e0856754
      internalID: -390868014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Fences_8
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 328202e2226fa1040bea580f8f7fa49b
      internalID: -1521644635
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Fences_9
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a0d33804069fafb4aacd8550b47bd6d4
      internalID: 1925122779
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Fences_10
      rect:
        serializedVersion: 2
        x: 1024
        y: 0
        width: 256
        height: 384
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e7ad9e2b019b2364cac69e6518ccec3b
      internalID: 1147963819
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 9d85f805e3f816c4db019df8733abb5a
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Isometric_Fences_0: -554281435
      Isometric_Fences_1: -509594886
      Isometric_Fences_10: 1147963819
      Isometric_Fences_2: -856146300
      Isometric_Fences_3: -1947363811
      Isometric_Fences_4: -48743700
      Isometric_Fences_5: 1029280952
      Isometric_Fences_6: 1035012717
      Isometric_Fences_7: -390868014
      Isometric_Fences_8: -1521644635
      Isometric_Fences_9: 1925122779
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
