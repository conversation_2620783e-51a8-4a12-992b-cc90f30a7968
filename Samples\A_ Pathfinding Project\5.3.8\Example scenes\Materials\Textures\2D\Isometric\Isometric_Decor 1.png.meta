fileFormatVersion: 2
guid: 50d9bf25e568e684d8ba0445a3eb37ee
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 0
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 256
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 16384
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Isometric_Decor 1_0
      rect:
        serializedVersion: 2
        x: 0
        y: 1523
        width: 369
        height: 349
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3d0397a7b19b1794cad2a34fced675e3
      internalID: -791599172
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_1
      rect:
        serializedVersion: 2
        x: 436
        y: 1523
        width: 369
        height: 349
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b1f1fd10a3f43c148af1c386d7d8bfe4
      internalID: -445106168
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_2
      rect:
        serializedVersion: 2
        x: 977
        y: 1495
        width: 170
        height: 172
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6ceb2450537e38144b8e29ae1402ea2c
      internalID: 818032526
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_3
      rect:
        serializedVersion: 2
        x: 1318
        y: 1118
        width: 730
        height: 754
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e18eaf56fbaa936438bd6b381d036fb2
      internalID: -531232897
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_4
      rect:
        serializedVersion: 2
        x: 154
        y: 1251
        width: 276
        height: 157
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ef2b49fd32438bc408e8c3f2eee4aadc
      internalID: 1519285603
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_13
      rect:
        serializedVersion: 2
        x: 989
        y: 1179
        width: 248
        height: 195
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eb56da12426710249a9cffb1b59dfd68
      internalID: 713345868
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_22
      rect:
        serializedVersion: 2
        x: 698
        y: 897
        width: 251
        height: 167
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 541d1980e2de9eb46bee3254d151f166
      internalID: 1381583820
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_23
      rect:
        serializedVersion: 2
        x: 1537
        y: 467
        width: 511
        height: 651
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 17adf5e293c79e549a5a1c481549cba4
      internalID: -1168248536
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_24
      rect:
        serializedVersion: 2
        x: 78
        y: 821
        width: 408
        height: 225
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: feba354581e1cff49977a32545da8605
      internalID: -945403782
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_35
      rect:
        serializedVersion: 2
        x: 1127
        y: 863
        width: 130
        height: 58
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b24f7a52a2d429f45b79045f3d4c067a
      internalID: 330422455
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_44
      rect:
        serializedVersion: 2
        x: 868
        y: 724
        width: 182
        height: 135
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e1c13e0563ec88c499bf224ea081fc3b
      internalID: -978051617
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_45
      rect:
        serializedVersion: 2
        x: 0
        y: 33
        width: 565
        height: 583
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 084481cf8669e4940a8d4f9b9befa42f
      internalID: 471539441
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_46
      rect:
        serializedVersion: 2
        x: 805
        y: 348
        width: 92
        height: 118
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c075d636c5d75374b9d59bf4b0211280
      internalID: -958668012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_47
      rect:
        serializedVersion: 2
        x: 1380
        y: 33
        width: 668
        height: 374
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8ff3ea27f10459f49b549d23a7fe3263
      internalID: 2026604544
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Isometric_Decor 1_48
      rect:
        serializedVersion: 2
        x: 1079
        y: 113
        width: 124
        height: 218
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7c8a6a061e6ace048af59fd24bff171e
      internalID: -2062810778
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 3de87d1e5aa819849844e47e1edce6ff
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Isometric_Decor 1_0: -791599172
      Isometric_Decor 1_1: -445106168
      Isometric_Decor 1_10: -2131013597
      Isometric_Decor 1_11: 1643293446
      Isometric_Decor 1_12: 1609098062
      Isometric_Decor 1_13: 713345868
      Isometric_Decor 1_14: 149875468
      Isometric_Decor 1_15: -444037262
      Isometric_Decor 1_16: 662047645
      Isometric_Decor 1_17: -462760667
      Isometric_Decor 1_18: 632247501
      Isometric_Decor 1_19: -719698070
      Isometric_Decor 1_2: 818032526
      Isometric_Decor 1_20: 316585743
      Isometric_Decor 1_21: -891299165
      Isometric_Decor 1_22: 1381583820
      Isometric_Decor 1_23: -1168248536
      Isometric_Decor 1_24: -945403782
      Isometric_Decor 1_25: -1603755639
      Isometric_Decor 1_26: 1497933504
      Isometric_Decor 1_27: -1933353087
      Isometric_Decor 1_28: -1057005466
      Isometric_Decor 1_29: 1467755872
      Isometric_Decor 1_3: -531232897
      Isometric_Decor 1_30: -485329534
      Isometric_Decor 1_31: -328689695
      Isometric_Decor 1_32: 921728908
      Isometric_Decor 1_33: 1356197066
      Isometric_Decor 1_34: 928114573
      Isometric_Decor 1_35: 330422455
      Isometric_Decor 1_36: 1929140553
      Isometric_Decor 1_37: -301338447
      Isometric_Decor 1_38: 524060707
      Isometric_Decor 1_39: 7371421
      Isometric_Decor 1_4: 1519285603
      Isometric_Decor 1_40: 314515454
      Isometric_Decor 1_41: 1884826666
      Isometric_Decor 1_42: -979661423
      Isometric_Decor 1_43: -1824640912
      Isometric_Decor 1_44: -978051617
      Isometric_Decor 1_45: 471539441
      Isometric_Decor 1_46: -958668012
      Isometric_Decor 1_47: 2026604544
      Isometric_Decor 1_48: -2062810778
      Isometric_Decor 1_49: 1916684505
      Isometric_Decor 1_5: 309250882
      Isometric_Decor 1_6: -1733200988
      Isometric_Decor 1_7: 1511278682
      Isometric_Decor 1_8: -1806410353
      Isometric_Decor 1_9: 209228316
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
