fileFormatVersion: 2
guid: 0b8cbced01d3121429ffe4e264e4a891
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 0
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 256
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 8192
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_0
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 740f1a711037f2144be71d1b9a8422ad
      internalID: 1485057799
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_1
      rect:
        serializedVersion: 2
        x: 512
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1e1e593c2f81b5945a5e04307211a6b9
      internalID: 432419609
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_2
      rect:
        serializedVersion: 2
        x: 1024
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0d401cae8b50d8a41862445f4ff7f76f
      internalID: 236065921
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_3
      rect:
        serializedVersion: 2
        x: 1536
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3548d5b3248a1a94eb389ea2830880ec
      internalID: 137727675
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_4
      rect:
        serializedVersion: 2
        x: 2048
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5d1bd2782f9f2eb48b062b98b29fe2f1
      internalID: 1052174296
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_5
      rect:
        serializedVersion: 2
        x: 2560
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 049ff5554e5ff3c4d9cd6778a6360095
      internalID: -105110049
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_6
      rect:
        serializedVersion: 2
        x: 3072
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c64de61768365aa41a0967d0d9603b4a
      internalID: -2025792790
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_7
      rect:
        serializedVersion: 2
        x: 3584
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 07f7c729318832940b21ecd2845af53b
      internalID: -543001661
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_8
      rect:
        serializedVersion: 2
        x: 4096
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9bef81f21289462468328639dfd83c27
      internalID: 1877345591
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_9
      rect:
        serializedVersion: 2
        x: 4608
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 444159f6a9c447844971d3af4a463abc
      internalID: -2025498050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_10
      rect:
        serializedVersion: 2
        x: 5120
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b4507a2cec4706140aae441e190b3bab
      internalID: -1543614504
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_11
      rect:
        serializedVersion: 2
        x: 5632
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2dac63aaed2031940b697be9041160b8
      internalID: 2006740228
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_12
      rect:
        serializedVersion: 2
        x: 6144
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0ac38230b72f8424fb05c9fdea77106f
      internalID: -1439211378
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_13
      rect:
        serializedVersion: 2
        x: 6656
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e22f39cc319bd0941a05e570ba8f15c8
      internalID: -1295530101
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_14
      rect:
        serializedVersion: 2
        x: 7168
        y: 768
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 86d34e44d5d7a1c41b4fb292cd338c50
      internalID: -1044314244
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_15
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ac23d2edb5ad1584c8c58305ddc49378
      internalID: -855543576
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_16
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2734eec685a2f2b45984286248a84dfc
      internalID: 1770236151
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_17
      rect:
        serializedVersion: 2
        x: 1024
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 36cc8a8202b42e14f9c88a9db5866d4e
      internalID: -1769535665
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_18
      rect:
        serializedVersion: 2
        x: 1536
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 42e3e75dece4c6d488a31d3d0627ebc3
      internalID: 1109999986
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_19
      rect:
        serializedVersion: 2
        x: 2048
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0fb5b1bc17320ee4092c120c5fe723f8
      internalID: -1871032116
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_20
      rect:
        serializedVersion: 2
        x: 2560
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d1dc807bdd6e08244ab83178de5c0ebe
      internalID: -1034802339
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_21
      rect:
        serializedVersion: 2
        x: 3072
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d0c6bb0161dad654889b98b47a8a006e
      internalID: -2118325544
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_22
      rect:
        serializedVersion: 2
        x: 3584
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e1ecdfda783e20e4f82ded8241c23f8b
      internalID: 1974883598
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_23
      rect:
        serializedVersion: 2
        x: 4096
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b29408dcc9cc2c6468c8da37cfad1714
      internalID: -2050990047
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_24
      rect:
        serializedVersion: 2
        x: 4608
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d98c44ff6f093b5489d870ce41f71348
      internalID: 725157131
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_25
      rect:
        serializedVersion: 2
        x: 5120
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4a11c9add0274254095c3d8c5097d9cc
      internalID: -1643413484
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_26
      rect:
        serializedVersion: 2
        x: 5632
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a09f6e801be27f341911975de399344d
      internalID: 1572873968
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_27
      rect:
        serializedVersion: 2
        x: 6144
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 776945e0bf051244bad0bfe9430945ba
      internalID: -1357432853
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_28
      rect:
        serializedVersion: 2
        x: 6656
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b665d2ee31f24ec4890cbd5707834f1e
      internalID: 714253364
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Idle_SpriteSheet_29
      rect:
        serializedVersion: 2
        x: 7168
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ab0a9b15caaf62440b4ffca412999062
      internalID: 1213610591
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: ec62524b34b597f47abb7bbc1c8e4602
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      2D_Chest_Idle_SpriteSheet_0: 1485057799
      2D_Chest_Idle_SpriteSheet_1: 432419609
      2D_Chest_Idle_SpriteSheet_10: -1543614504
      2D_Chest_Idle_SpriteSheet_11: 2006740228
      2D_Chest_Idle_SpriteSheet_12: -1439211378
      2D_Chest_Idle_SpriteSheet_13: -1295530101
      2D_Chest_Idle_SpriteSheet_14: -1044314244
      2D_Chest_Idle_SpriteSheet_15: -855543576
      2D_Chest_Idle_SpriteSheet_16: 1770236151
      2D_Chest_Idle_SpriteSheet_17: -1769535665
      2D_Chest_Idle_SpriteSheet_18: 1109999986
      2D_Chest_Idle_SpriteSheet_19: -1871032116
      2D_Chest_Idle_SpriteSheet_2: 236065921
      2D_Chest_Idle_SpriteSheet_20: -1034802339
      2D_Chest_Idle_SpriteSheet_21: -2118325544
      2D_Chest_Idle_SpriteSheet_22: 1974883598
      2D_Chest_Idle_SpriteSheet_23: -2050990047
      2D_Chest_Idle_SpriteSheet_24: 725157131
      2D_Chest_Idle_SpriteSheet_25: -1643413484
      2D_Chest_Idle_SpriteSheet_26: 1572873968
      2D_Chest_Idle_SpriteSheet_27: -1357432853
      2D_Chest_Idle_SpriteSheet_28: 714253364
      2D_Chest_Idle_SpriteSheet_29: 1213610591
      2D_Chest_Idle_SpriteSheet_3: 137727675
      2D_Chest_Idle_SpriteSheet_4: 1052174296
      2D_Chest_Idle_SpriteSheet_5: -105110049
      2D_Chest_Idle_SpriteSheet_6: -2025792790
      2D_Chest_Idle_SpriteSheet_7: -543001661
      2D_Chest_Idle_SpriteSheet_8: 1877345591
      2D_Chest_Idle_SpriteSheet_9: -2025498050
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
