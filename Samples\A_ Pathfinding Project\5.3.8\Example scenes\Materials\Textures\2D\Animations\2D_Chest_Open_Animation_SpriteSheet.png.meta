fileFormatVersion: 2
guid: 1c1f1d01ad00ab04eb01f0facbeedd7b
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 0
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 256
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 8192
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0591746dcc5d4af4f8b5b97024bae200
      internalID: 656620933
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_1
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b390f980276ce594d8a0ec71a893d1a6
      internalID: -500011042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_2
      rect:
        serializedVersion: 2
        x: 1024
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e3ede9e1e0107744ba33dddae68b7a38
      internalID: -1615411543
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_3
      rect:
        serializedVersion: 2
        x: 1536
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b1b11572f5fb5c84aa663dbae1d981a5
      internalID: -306654916
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_4
      rect:
        serializedVersion: 2
        x: 2048
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7a5d47e80cde62b4885707377102aff8
      internalID: -607436004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_5
      rect:
        serializedVersion: 2
        x: 2560
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7bef3ab3c162115458eecfc10fac9082
      internalID: -750938650
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_6
      rect:
        serializedVersion: 2
        x: 3072
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f4b15ff710f788b4aa88727533e4f04a
      internalID: 1744679747
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_7
      rect:
        serializedVersion: 2
        x: 3584
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ada22991379a557459cbfed0c5dd16a3
      internalID: -1055880508
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_8
      rect:
        serializedVersion: 2
        x: 4096
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 53a68d0ed46d1a0498fd3d6e043bdb4d
      internalID: 665477569
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_9
      rect:
        serializedVersion: 2
        x: 4608
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1aab51401109a65408034ea67101d001
      internalID: -1587794805
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_10
      rect:
        serializedVersion: 2
        x: 5120
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9665c0f53d5245c40b339ec1f69ba3d3
      internalID: -454346871
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_11
      rect:
        serializedVersion: 2
        x: 5632
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ffc4f5f3677dc0f4e8f21e916f11bf37
      internalID: -1892064395
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: 2D_Chest_Open_Animation_SpriteSheet_12
      rect:
        serializedVersion: 2
        x: 6144
        y: 0
        width: 512
        height: 768
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b8acf9c68d0a16549aadbe6f7ec6023c
      internalID: 1034063805
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: d794835153c439343a751ee6bd7c1944
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      2D_Chest_Open_Animation_SpriteSheet_0: 656620933
      2D_Chest_Open_Animation_SpriteSheet_1: -500011042
      2D_Chest_Open_Animation_SpriteSheet_10: -454346871
      2D_Chest_Open_Animation_SpriteSheet_11: -1892064395
      2D_Chest_Open_Animation_SpriteSheet_12: 1034063805
      2D_Chest_Open_Animation_SpriteSheet_2: -1615411543
      2D_Chest_Open_Animation_SpriteSheet_3: -306654916
      2D_Chest_Open_Animation_SpriteSheet_4: -607436004
      2D_Chest_Open_Animation_SpriteSheet_5: -750938650
      2D_Chest_Open_Animation_SpriteSheet_6: 1744679747
      2D_Chest_Open_Animation_SpriteSheet_7: -1055880508
      2D_Chest_Open_Animation_SpriteSheet_8: 665477569
      2D_Chest_Open_Animation_SpriteSheet_9: -1587794805
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
